# PowerShell script to test MCP API

# Test CLI-MCP-SERVER
$cliPayload = @{
    selected_server_credentials = @{}
    client_details = @{
        api_key = "AIzaSyARTvccHRFHDYY2oqy9aHZDN4Z5dk-EJt4"
        temperature = 0.1
        max_token = 20000
        input = "List the contents of the current directory"
        input_type = "text"
        prompt = "you are a helpful assistant"
        chat_model = "gemini-2.0-flash"
        chat_history = @(@{role = "user"; content = "Hello"})
    }
    selected_client = "MCP_CLIENT_GEMINI"
    selected_servers = @("CLI-MCP-SERVER")
} | ConvertTo-Json -Depth 10

Write-Host "Testing CLI-MCP-SERVER..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "http://localhost:5001/api/v1/mcp/process_message" -Method POST -Body $cliPayload -ContentType "application/json"
    Write-Host "✅ CLI-MCP-SERVER Response:" -ForegroundColor Green
    $response | ConvertTo-Json -Depth 10
} catch {
    Write-Host "❌ CLI-MCP-SERVER Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n" + "="*50 + "`n"

# Test MCP-JOOMLA
$joomlaPayload = @{
    selected_server_credentials = @{
        "MCP-JOOMLA" = @{
            base_url = "https://ashhs.joomla.com/"
            bearer_token = "c2hhMjU2Ojg4NDplNzQxYTM0MzQ3NjQ0ZDYzNWE3NTVjNmYwN2JkNTk3YjIwZjM3M2U5MjIzYzI3MzNkNTE1NTJhZjBjNDRlMWFj"
        }
    }
    client_details = @{
        api_key = "AIzaSyARTvccHRFHDYY2oqy9aHZDN4Z5dk-EJt4"
        temperature = 0.1
        max_token = 20000
        input = "Get all categories from Joomla"
        input_type = "text"
        prompt = "you are a helpful assistant"
        chat_model = "gemini-2.0-flash"
        chat_history = @(@{role = "user"; content = "Hello"})
    }
    selected_client = "MCP_CLIENT_GEMINI"
    selected_servers = @("MCP-JOOMLA")
} | ConvertTo-Json -Depth 10

Write-Host "Testing MCP-JOOMLA..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "http://localhost:5001/api/v1/mcp/process_message" -Method POST -Body $joomlaPayload -ContentType "application/json"
    Write-Host "✅ MCP-JOOMLA Response:" -ForegroundColor Green
    $response | ConvertTo-Json -Depth 10
} catch {
    Write-Host "❌ MCP-JOOMLA Error: $($_.Exception.Message)" -ForegroundColor Red
}
