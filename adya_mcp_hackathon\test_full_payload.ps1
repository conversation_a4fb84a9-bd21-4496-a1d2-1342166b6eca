# Test with full CLI-MCP-SERVER payload
$cliPayload = @{
    "selected_server_credentials" = @{}
    "client_details" = @{
        "api_key" = "AIzaSyARTvccHRFHDYY2oqy9aHZDN4Z5dk-EJt4"
        "temperature" = 0.1
        "max_token" = 20000
        "input" = "List the contents of the current directory"
        "input_type" = "text"
        "prompt" = "you are a helpful assistant"
        "chat_model" = "gemini-2.0-flash"
        "chat_history" = @(
            @{
                "role" = "user"
                "content" = "Hello"
            }
        )
        "is_stream" = $false
    }
    "selected_client" = "MCP_CLIENT_GEMINI"
    "selected_servers" = @("CLI-MCP-SERVER")
}

Write-Host "Testing CLI-MCP-SERVER with full payload..."

try {
    $response = Invoke-RestMethod -Uri "http://localhost:5001/api/v1/mcp/process_message" -Method POST -Body ($cliPayload | ConvertTo-Json -Depth 10) -ContentType "application/json"
    Write-Host "✅ CLI Response:" -ForegroundColor Green
    Write-Host ($response | ConvertTo-Json -Depth 10)
} catch {
    Write-Host "❌ CLI Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test with MCP-JOOMLA payload
$joomlaPayload = @{
    "selected_server_credentials" = @{
        "MCP-JOOMLA" = @{
            "base_url" = "https://joomla.adyawinsa.com"
            "bearer_token" = "ZWQ5YzJhNzEtNzNhNy00YzNhLWI5YzMtNzNhN2M5YzJhNzE6ZWQ5YzJhNzEtNzNhNy00YzNhLWI5YzMtNzNhN2M5YzJhNzE="
        }
    }
    "client_details" = @{
        "api_key" = "AIzaSyARTvccHRFHDYY2oqy9aHZDN4Z5dk-EJt4"
        "temperature" = 0.1
        "max_token" = 20000
        "input" = "Get all Joomla categories"
        "input_type" = "text"
        "prompt" = "you are a helpful assistant"
        "chat_model" = "gemini-2.0-flash"
        "chat_history" = @(
            @{
                "role" = "user"
                "content" = "Hello"
            }
        )
        "is_stream" = $false
    }
    "selected_client" = "MCP_CLIENT_GEMINI"
    "selected_servers" = @("MCP-JOOMLA")
}

Write-Host "`nTesting MCP-JOOMLA with full payload..."

try {
    $response = Invoke-RestMethod -Uri "http://localhost:5001/api/v1/mcp/process_message" -Method POST -Body ($joomlaPayload | ConvertTo-Json -Depth 10) -ContentType "application/json"
    Write-Host "✅ JOOMLA Response:" -ForegroundColor Green
    Write-Host ($response | ConvertTo-Json -Depth 10)
} catch {
    Write-Host "❌ JOOMLA Error: $($_.Exception.Message)" -ForegroundColor Red
}
