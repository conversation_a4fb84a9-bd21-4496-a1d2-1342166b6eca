# Updated MCP-JOOM<PERSON> Payloads - Part 2

## 9. Move Article to Trash 🔄 NEEDS TESTING
```json
{
    "selected_server_credentials": {
        "MCP-JOOMLA": {
            "base_url": "https://ashhs.joomla.com/",
            "bearer_token": "c2hhMjU2Ojg4NDplNzQxYTM0MzQ3NjQ0ZDYzNWE3NTVjNmYwN2JkNTk3YjIwZjM3M2U5MjIzYzI3MzNkNTE1NTJhZjBjNDRlMWFj"
        }
    },
    "client_details": {
        "api_key": "AIzaSyDZsa-ifofUJuYsLu6-R5_0pxmrkB_L7fQ",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Move article with ID 1 to trash in Joomla base_url: https://ashhs.joomla.com/, bearer_token: c2hhMjU2Ojg4NDplNzQxYTM0MzQ3NjQ0ZDYzNWE3NTVjNmYwN2JkNTk3YjIwZjM3M2U5MjIzYzI3MzNkNTE1NTJhZjBjNDRlMWFj",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "base_url": "https://ashhs.joomla.com/",
        "bearer_token": "c2hhMjU2Ojg4NDplNzQxYTM0MzQ3NjQ0ZDYzNWE3NTVjNmYwN2JkNTk3YjIwZjM3M2U5MjIzYzI3MzNkNTE1NTJhZjBjNDRlMWFj",
        "chat_model": "gemini-2.0-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["MCP-JOOMLA"]
}
```

## 10. Update Article 🔄 NEEDS TESTING
```json
{
    "selected_server_credentials": {
        "MCP-JOOMLA": {
            "base_url": "https://ashhs.joomla.com/",
            "bearer_token": "c2hhMjU2Ojg4NDplNzQxYTM0MzQ3NjQ0ZDYzNWE3NTVjNmYwN2JkNTk3YjIwZjM3M2U5MjIzYzI3MzNkNTE1NTJhZjBjNDRlMWFj"
        }
    },
    "client_details": {
        "api_key": "AIzaSyDZsa-ifofUJuYsLu6-R5_0pxmrkB_L7fQ",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Update article with ID 1 in Joomla with new title 'Updated Article' base_url: https://ashhs.joomla.com/, bearer_token: c2hhMjU2Ojg4NDplNzQxYTM0MzQ3NjQ0ZDYzNWE3NTVjNmYwN2JkNTk3YjIwZjM3M2U5MjIzYzI3MzNkNTE1NTJhZjBjNDRlMWFj",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "base_url": "https://ashhs.joomla.com/",
        "bearer_token": "c2hhMjU2Ojg4NDplNzQxYTM0MzQ3NjQ0ZDYzNWE3NTVjNmYwN2JkNTk3YjIwZjM3M2U5MjIzYzI3MzNkNTE1NTJhZjBjNDRlMWFj",
        "chat_model": "gemini-2.0-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["MCP-JOOMLA"]
}
```

## 11. Get User By ID 🔄 NEEDS TESTING
```json
{
    "selected_server_credentials": {
        "MCP-JOOMLA": {
            "base_url": "https://ashhs.joomla.com/",
            "bearer_token": "c2hhMjU2Ojg4NDplNzQxYTM0MzQ3NjQ0ZDYzNWE3NTVjNmYwN2JkNTk3YjIwZjM3M2U5MjIzYzI3MzNkNTE1NTJhZjBjNDRlMWFj"
        }
    },
    "client_details": {
        "api_key": "AIzaSyDZsa-ifofUJuYsLu6-R5_0pxmrkB_L7fQ",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Get user with ID 885 from Joomla base_url: https://ashhs.joomla.com/, bearer_token: c2hhMjU2Ojg4NDplNzQxYTM0MzQ3NjQ0ZDYzNWE3NTVjNmYwN2JkNTk3YjIwZjM3M2U5MjIzYzI3MzNkNTE1NTJhZjBjNDRlMWFj",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "base_url": "https://ashhs.joomla.com/",
        "bearer_token": "c2hhMjU2Ojg4NDplNzQxYTM0MzQ3NjQ0ZDYzNWE3NTVjNmYwN2JkNTk3YjIwZjM3M2U5MjIzYzI3MzNkNTE1NTJhZjBjNDRlMWFj",
        "chat_model": "gemini-2.0-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["MCP-JOOMLA"]
}
```

## 12. Get Joomla Menus 🔄 NEEDS TESTING
```json
{
    "selected_server_credentials": {
        "MCP-JOOMLA": {
            "base_url": "https://ashhs.joomla.com/",
            "bearer_token": "c2hhMjU2Ojg4NDplNzQxYTM0MzQ3NjQ0ZDYzNWE3NTVjNmYwN2JkNTk3YjIwZjM3M2U5MjIzYzI3MzNkNTE1NTJhZjBjNDRlMWFj"
        }
    },
    "client_details": {
        "api_key": "AIzaSyDZsa-ifofUJuYsLu6-R5_0pxmrkB_L7fQ",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Get all menus from Joomla base_url: https://ashhs.joomla.com/, bearer_token: c2hhMjU2Ojg4NDplNzQxYTM0MzQ3NjQ0ZDYzNWE3NTVjNmYwN2JkNTk3YjIwZjM3M2U5MjIzYzI3MzNkNTE1NTJhZjBjNDRlMWFj",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "base_url": "https://ashhs.joomla.com/",
        "bearer_token": "c2hhMjU2Ojg4NDplNzQxYTM0MzQ3NjQ0ZDYzNWE3NTVjNmYwN2JkNTk3YjIwZjM3M2U5MjIzYzI3MzNkNTE1NTJhZjBjNDRlMWFj",
        "chat_model": "gemini-2.0-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["MCP-JOOMLA"]
}
```

---

## Summary of All 12 MCP-JOOMLA Tools

✅ **VERIFIED WORKING (3 tools):**
1. `get_joomla_users` - Returns real user data
2. `get_joomla_categories` - Returns categories  
3. `get_joomla_articles` - Returns articles (empty list currently)

❌ **HAS BUG (1 tool):**
4. `get_site_info` - Implementation error: "'list' object has no attribute 'get'"

🔄 **NEEDS TESTING (8 tools):**
5. `create_article` - Create new articles
6. `get_article_by_id` - Get specific article by ID
7. `search_articles` - Search articles by criteria
8. `manage_article_state` - Change article publication state
9. `move_article_to_trash` - Move articles to trash
10. `update_article` - Update existing articles
11. `get_user_by_id` - Get specific user by ID
12. `get_joomla_menus` - Get menu structures

All payloads now use:
- New Gemini API key: `AIzaSyDZsa-ifofUJuYsLu6-R5_0pxmrkB_L7fQ`
- Dual credential placement in both `selected_server_credentials` and `client_details`
- Embedded credentials in the input text for proper functionality
