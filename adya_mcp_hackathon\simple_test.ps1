# Simple test to check if server is responding

Write-Host "Testing server connectivity..." -ForegroundColor Yellow

try {
    $response = Invoke-RestMethod -Uri "http://localhost:5001/health" -Method GET
    Write-Host "✅ Health check response: $response" -ForegroundColor Green
} catch {
    Write-Host "❌ Health check failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test with minimal CLI payload
$minimalPayload = @{
    selected_server_credentials = @{}
    client_details = @{
        api_key = "test"
        input = "test"
        chat_model = "gemini-2.0-flash"
        chat_history = @()
    }
    selected_client = "MCP_CLIENT_GEMINI"
    selected_servers = @("CLI-MCP-SERVER")
} | ConvertTo-Json -Depth 10

Write-Host "`nTesting minimal CLI payload..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "http://localhost:5001/api/v1/mcp/process_message" -Method POST -Body $minimalPayload -ContentType "application/json"
    Write-Host "✅ Minimal CLI Response:" -ForegroundColor Green
    $response | ConvertTo-Json -Depth 5
} catch {
    Write-Host "❌ Minimal CLI Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response body: $responseBody" -ForegroundColor Red
    }
}
