#!/usr/bin/env python3

import asyncio
import sys
import os

# Add the clients directory to the path
sys.path.append('mcp_servers/python/clients')

async def start_server_debug():
    try:
        print("🔍 Starting server with debug output...")
        
        # Import server components
        from quart import Quart
        from hypercorn.asyncio import serve
        from hypercorn.config import Config
        from contextlib import AsyncExitStack
        from src.server_connection import initialize_all_mcp
        
        # Create the app
        app = Quart(__name__)
        
        # Initialize MCP servers
        print("🚀 Initializing MCP servers...")
        app.mcp_exit_stack = AsyncExitStack()
        await app.mcp_exit_stack.__aenter__()
        
        try:
            await initialize_all_mcp(app.mcp_exit_stack)
            print("✅ MCP servers initialized successfully")
            
            # Add a simple test route
            @app.route("/health", methods=["GET"])
            async def health_check():
                return {"status": "healthy", "message": "Server is running"}
            
            @app.route("/api/v1/mcp/process_message", methods=["POST"])
            async def process_message():
                try:
                    from quart import request, jsonify
                    data = await request.get_json()
                    print(f"🔍 DEBUG - Received request data: {data}")
                    
                    # Set streaming to false
                    if "client_details" in data:
                        data["client_details"]["is_stream"] = False
                    
                    # Validation check
                    print(f"🔍 DEBUG - Starting validation...")
                    from src.client_and_server_validation import client_and_server_validation
                    validation_result = await client_and_server_validation(data, {"streamCallbacks": None, "is_stream": False})
                    print(f"🔍 DEBUG - Validation result: {validation_result}")
                    
                    if not validation_result["status"]:
                        print(f"❌ DEBUG - Validation failed: {validation_result['error']}")
                        return jsonify({
                            "Data": None,
                            "Error": validation_result["error"],
                            "Status": False
                        }), 200
                    
                    # Execute
                    print(f"🎯 DEBUG - Starting execution...")
                    from src.client_and_server_execution import client_and_server_execution
                    execution_result = await client_and_server_execution(data, {"streamCallbacks": None, "is_stream": False})
                    
                    print(f"✅ DEBUG - Execution completed: Status={execution_result.Status}")
                    
                    return jsonify({
                        "Data": execution_result.Data,
                        "Error": execution_result.Error,
                        "Status": execution_result.Status
                    }), 200
                    
                except Exception as e:
                    print(f"❌ DEBUG - Exception in endpoint: {e}")
                    import traceback
                    traceback.print_exc()
                    return jsonify({
                        "Data": None,
                        "Error": str(e),
                        "Status": False
                    }), 500
            
            # Create config
            config = Config()
            config.bind = ["0.0.0.0:5001"]
            
            print("🌟 Starting server on http://0.0.0.0:5001")
            print("📡 Endpoints available:")
            print("  - GET  /health")
            print("  - POST /api/v1/mcp/process_message")
            
            # Start server
            await serve(app, config)
            
        finally:
            if hasattr(app, 'mcp_exit_stack') and app.mcp_exit_stack:
                await app.mcp_exit_stack.__aexit__(None, None, None)
                print("✅ MCP servers cleaned up")
            
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(start_server_debug())
