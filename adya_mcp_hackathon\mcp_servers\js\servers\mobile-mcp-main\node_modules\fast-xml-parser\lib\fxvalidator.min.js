!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.XMLValidator=t():e.XMLValidator=t()}(this,(()=>(()=>{"use strict";var e={d:(t,r)=>{for(var n in r)e.o(r,n)&&!e.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:r[n]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{validate:()=>l});var r=":A-Za-z_\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD",n=new RegExp("^["+r+"]["+r+"\\-.\\d\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),i=function(e){return!(null==n.exec(e))},a={allowBooleanAttributes:!1,unpairedTags:[]};function l(e,t){t=Object.assign({},a,t);var r=[],n=!1,l=!1;"\ufeff"===e[0]&&(e=e.substr(1));for(var s=0;s<e.length;s++)if("<"===e[s]&&"?"===e[s+1]){if((s=u(e,s+=2)).err)return s}else{if("<"!==e[s]){if(o(e[s]))continue;return p("InvalidChar","char '"+e[s]+"' is not expected.",m(e,s))}var g=s;if("!"===e[++s]){s=f(e,s);continue}var v=!1;"/"===e[s]&&(v=!0,s++);for(var b="";s<e.length&&">"!==e[s]&&" "!==e[s]&&"\t"!==e[s]&&"\n"!==e[s]&&"\r"!==e[s];s++)b+=e[s];if("/"===(b=b.trim())[b.length-1]&&(b=b.substring(0,b.length-1),s--),!i(b))return p("InvalidTag",0===b.trim().length?"Invalid space after '<'.":"Tag '"+b+"' is an invalid name.",m(e,s));var F=d(e,s);if(!1===F)return p("InvalidAttr","Attributes for '"+b+"' have open quote.",m(e,s));var I=F.value;if(s=F.index,"/"===I[I.length-1]){var x=s-I.length,A=c(I=I.substring(0,I.length-1),t);if(!0!==A)return p(A.err.code,A.err.msg,m(e,x+A.err.line));n=!0}else if(v){if(!F.tagClosed)return p("InvalidTag","Closing tag '"+b+"' doesn't have proper closing.",m(e,s));if(I.trim().length>0)return p("InvalidTag","Closing tag '"+b+"' can't have attributes or invalid starting.",m(e,g));if(0===r.length)return p("InvalidTag","Closing tag '"+b+"' has not been opened.",m(e,g));var y=r.pop();if(b!==y.tagName){var C=m(e,y.tagStartPos);return p("InvalidTag","Expected closing tag '"+y.tagName+"' (opened in line "+C.line+", col "+C.col+") instead of closing tag '"+b+"'.",m(e,g))}0==r.length&&(l=!0)}else{var T=c(I,t);if(!0!==T)return p(T.err.code,T.err.msg,m(e,s-I.length+T.err.line));if(!0===l)return p("InvalidXml","Multiple possible root nodes found.",m(e,s));-1!==t.unpairedTags.indexOf(b)||r.push({tagName:b,tagStartPos:g}),n=!0}for(s++;s<e.length;s++)if("<"===e[s]){if("!"===e[s+1]){s=f(e,++s);continue}if("?"!==e[s+1])break;if((s=u(e,++s)).err)return s}else if("&"===e[s]){var S=h(e,s);if(-1==S)return p("InvalidChar","char '&' is not expected.",m(e,s));s=S}else if(!0===l&&!o(e[s]))return p("InvalidXml","Extra text at the end",m(e,s));"<"===e[s]&&s--}return n?1==r.length?p("InvalidTag","Unclosed tag '"+r[0].tagName+"'.",m(e,r[0].tagStartPos)):!(r.length>0)||p("InvalidXml","Invalid '"+JSON.stringify(r.map((function(e){return e.tagName})),null,4).replace(/\r?\n/g,"")+"' found.",{line:1,col:1}):p("InvalidXml","Start tag expected.",1)}function o(e){return" "===e||"\t"===e||"\n"===e||"\r"===e}function u(e,t){for(var r=t;t<e.length;t++)if("?"!=e[t]&&" "!=e[t]);else{var n=e.substr(r,t-r);if(t>5&&"xml"===n)return p("InvalidXml","XML declaration allowed only at the start of the document.",m(e,t));if("?"==e[t]&&">"==e[t+1]){t++;break}}return t}function f(e,t){if(e.length>t+5&&"-"===e[t+1]&&"-"===e[t+2]){for(t+=3;t<e.length;t++)if("-"===e[t]&&"-"===e[t+1]&&">"===e[t+2]){t+=2;break}}else if(e.length>t+8&&"D"===e[t+1]&&"O"===e[t+2]&&"C"===e[t+3]&&"T"===e[t+4]&&"Y"===e[t+5]&&"P"===e[t+6]&&"E"===e[t+7]){var r=1;for(t+=8;t<e.length;t++)if("<"===e[t])r++;else if(">"===e[t]&&0==--r)break}else if(e.length>t+9&&"["===e[t+1]&&"C"===e[t+2]&&"D"===e[t+3]&&"A"===e[t+4]&&"T"===e[t+5]&&"A"===e[t+6]&&"["===e[t+7])for(t+=8;t<e.length;t++)if("]"===e[t]&&"]"===e[t+1]&&">"===e[t+2]){t+=2;break}return t}var s='"',g="'";function d(e,t){for(var r="",n="",i=!1;t<e.length;t++){if(e[t]===s||e[t]===g)""===n?n=e[t]:n!==e[t]||(n="");else if(">"===e[t]&&""===n){i=!0;break}r+=e[t]}return""===n&&{value:r,index:t,tagClosed:i}}var v=new RegExp("(\\s*)([^\\s=]+)(\\s*=)?(\\s*(['\"])(([\\s\\S])*?)\\5)?","g");function c(e,t){for(var r=function(e,t){for(var r=[],n=t.exec(e);n;){var i=[];i.startIndex=t.lastIndex-n[0].length;for(var a=n.length,l=0;l<a;l++)i.push(n[l]);r.push(i),n=t.exec(e)}return r}(e,v),n={},i=0;i<r.length;i++){if(0===r[i][1].length)return p("InvalidAttr","Attribute '"+r[i][2]+"' has no space in starting.",F(r[i]));if(void 0!==r[i][3]&&void 0===r[i][4])return p("InvalidAttr","Attribute '"+r[i][2]+"' is without value.",F(r[i]));if(void 0===r[i][3]&&!t.allowBooleanAttributes)return p("InvalidAttr","boolean attribute '"+r[i][2]+"' is not allowed.",F(r[i]));var a=r[i][2];if(!b(a))return p("InvalidAttr","Attribute '"+a+"' is an invalid name.",F(r[i]));if(n.hasOwnProperty(a))return p("InvalidAttr","Attribute '"+a+"' is repeated.",F(r[i]));n[a]=1}return!0}function h(e,t){if(";"===e[++t])return-1;if("#"===e[t])return function(e,t){var r=/\d/;for("x"===e[t]&&(t++,r=/[\da-fA-F]/);t<e.length;t++){if(";"===e[t])return t;if(!e[t].match(r))break}return-1}(e,++t);for(var r=0;t<e.length;t++,r++)if(!(e[t].match(/\w/)&&r<20)){if(";"===e[t])break;return-1}return t}function p(e,t,r){return{err:{code:e,msg:t,line:r.line||r,col:r.col}}}function b(e){return i(e)}function m(e,t){var r=e.substring(0,t).split(/\r?\n/);return{line:r.length,col:r[r.length-1].length+1}}function F(e){return e.startIndex+e[1].length}return t})()));
//# sourceMappingURL=fxvalidator.min.js.map