#!/usr/bin/env python3

import asyncio
import sys
import os

# Add the clients directory to the path
sys.path.append('mcp_servers/python/clients')

async def test_server_initialization():
    try:
        print("🔍 Testing server initialization...")
        
        from src.server_connection import initialize_all_mcp, MCPServers
        from contextlib import AsyncExitStack
        
        exit_stack = AsyncExitStack()
        await exit_stack.__aenter__()
        
        try:
            print("🚀 Initializing all MCP servers...")
            success = await initialize_all_mcp(exit_stack)
            
            print(f"\n✅ Initialization result: {success}")
            print(f"📊 Available servers: {list(MCPServers.keys())}")
            print(f"📊 Server count: {len(MCPServers)}")
            
            for server_name, session in MCPServers.items():
                try:
                    tools_response = await session.list_tools()
                    tool_names = [tool.name for tool in tools_response.tools]
                    print(f"🔧 {server_name}: {len(tool_names)} tools - {tool_names[:3]}{'...' if len(tool_names) > 3 else ''}")
                except Exception as e:
                    print(f"❌ {server_name}: Error listing tools - {e}")
                    
        finally:
            await exit_stack.__aexit__(None, None, None)
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_server_initialization())
