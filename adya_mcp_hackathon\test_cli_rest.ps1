# Test CLI-MCP-SERVER through REST API
$cliPayload = @{
    "selected_server_credentials" = @{}
    "client_details" = @{
        "api_key" = "AIzaSyARTvccHRFHDYY2oqy9aHZDN4Z5dk-EJt4"
        "temperature" = 0.1
        "max_token" = 20000
        "input" = "List the contents of the current directory"
        "input_type" = "text"
        "prompt" = "you are a helpful assistant"
        "chat_model" = "gemini-2.0-flash"
        "chat_history" = @(@{
            "role" = "user"
            "content" = "Hello"
        })
        "is_stream" = $false
    }
    "selected_client" = "MCP_CLIENT_GEMINI"
    "selected_servers" = @("CLI-MCP-SERVER")
}

$cliJson = $cliPayload | ConvertTo-Json -Depth 10
Write-Host "🔍 CLI Payload JSON:"
Write-Host $cliJson

Write-Host "`n🚀 Testing CLI-MCP-SERVER through REST API..."

try {
    $response = Invoke-RestMethod -Uri "http://localhost:5001/api/v1/mcp/process_message" -Method POST -Body $cliJson -ContentType "application/json"
    Write-Host "✅ Response received:"
    Write-Host "Status: $($response.Status)"
    Write-Host "Error: $($response.Error)"
    if ($response.Data) {
        Write-Host "Data: $($response.Data | ConvertTo-Json -Depth 5)"
    }
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        Write-Host "Response: $($_.Exception.Response)"
    }
}
