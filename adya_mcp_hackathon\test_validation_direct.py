#!/usr/bin/env python3

import asyncio
import sys
import os

# Add the clients directory to the path
sys.path.append('mcp_servers/python/clients')

async def test_validation():
    try:
        # Import the validation function
        from src.client_and_server_validation import client_and_server_validation
        
        # Test CLI-MCP-SERVER payload
        cli_payload = {
            "selected_server_credentials": {},
            "client_details": {
                "api_key": "AIzaSyARTvccHRFHDYY2oqy9aHZDN4Z5dk-EJt4",
                "temperature": 0.1,
                "max_token": 20000,
                "input": "List the contents of the current directory",
                "input_type": "text",
                "prompt": "you are a helpful assistant",
                "chat_model": "gemini-2.0-flash",
                "chat_history": [{"role": "user", "content": "Hello"}]
            },
            "selected_client": "MCP_CLIENT_GEMINI",
            "selected_servers": ["CLI-MCP-SERVER"]
        }
        
        print("=== Testing CLI-MCP-SERVER Validation ===")
        print(f"Payload: {cli_payload}")
        
        # Initialize MCP servers first
        from src.server_connection import initialize_all_mcp
        from contextlib import AsyncExitStack
        
        exit_stack = AsyncExitStack()
        await exit_stack.__aenter__()
        
        try:
            print("Initializing MCP servers...")
            await initialize_all_mcp(exit_stack)
            print("MCP servers initialized successfully")
            
            # Now test validation
            result = await client_and_server_validation(cli_payload, {"streamCallbacks": None, "is_stream": False})
            print(f"Validation result: {result}")
            
            if result["status"]:
                print("✅ Validation PASSED")
            else:
                print(f"❌ Validation FAILED: {result['error']}")
                
        finally:
            await exit_stack.__aexit__(None, None, None)
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_validation())
