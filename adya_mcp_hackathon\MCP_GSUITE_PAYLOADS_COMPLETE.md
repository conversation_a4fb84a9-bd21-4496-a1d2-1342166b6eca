# MCP-GSUITE Complete Payloads - Gemini 2.5 Flash

## Overview
Complete payload documentation for all 18 MCP-GSUITE tools using Gemini 2.5 Flash API.

**API Configuration:**
- Model: `gemini-2.5-flash`
- API Key: `AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU`
- Client: `MCP_CLIENT_GEMINI`

**Server Status:** ✅ INITIALIZED AND READY - 18 tools available

**Note:** MCP-GSUITE requires Google OAuth credentials. Update the credentials_path and token_path with actual file paths.

---

## Gmail Tools

### 1. query_gmail_emails

```json
{
    "selected_server_credentials": {
        "MCP-GSUITE": {
            "credentials_path": "/path/to/google/credentials.json",
            "token_path": "/path/to/google/token.json"
        }
    },
    "client_details": {
        "api_key": "AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Use the query_gmail_emails tool to search for emails with query 'from:<EMAIL>'",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "chat_model": "gemini-2.5-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["MCP-GSUITE"]
}
```

### 2. get_gmail_email

```json
{
    "selected_server_credentials": {
        "MCP-GSUITE": {
            "credentials_path": "/path/to/google/credentials.json",
            "token_path": "/path/to/google/token.json"
        }
    },
    "client_details": {
        "api_key": "AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Use the get_gmail_email tool to get email with ID '12345'",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "chat_model": "gemini-2.5-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["MCP-GSUITE"]
}
```

### 3. create_gmail_draft

```json
{
    "selected_server_credentials": {
        "MCP-GSUITE": {
            "credentials_path": "/path/to/google/credentials.json",
            "token_path": "/path/to/google/token.json"
        }
    },
    "client_details": {
        "api_key": "AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Use the create_gmail_draft tool to create a draft email to '<EMAIL>' with subject 'Test Subject' and body 'Test Body'",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "chat_model": "gemini-2.5-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["MCP-GSUITE"]
}
```

### 4. delete_gmail_draft

```json
{
    "selected_server_credentials": {
        "MCP-GSUITE": {
            "credentials_path": "/path/to/google/credentials.json",
            "token_path": "/path/to/google/token.json"
        }
    },
    "client_details": {
        "api_key": "AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Use the delete_gmail_draft tool to delete draft with ID '12345'",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "chat_model": "gemini-2.5-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["MCP-GSUITE"]
}
```

### 5. reply_gmail_email

```json
{
    "selected_server_credentials": {
        "MCP-GSUITE": {
            "credentials_path": "/path/to/google/credentials.json",
            "token_path": "/path/to/google/token.json"
        }
    },
    "client_details": {
        "api_key": "AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Use the reply_gmail_email tool to reply to email with ID '12345' with message 'Thank you for your email'",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "chat_model": "gemini-2.5-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["MCP-GSUITE"]
}
```

### 6. get_gmail_attachment

```json
{
    "selected_server_credentials": {
        "MCP-GSUITE": {
            "credentials_path": "/path/to/google/credentials.json",
            "token_path": "/path/to/google/token.json"
        }
    },
    "client_details": {
        "api_key": "AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Use the get_gmail_attachment tool to get attachment with ID 'attachment123' from email ID '12345'",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "chat_model": "gemini-2.5-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["MCP-GSUITE"]
}
```

### 7. bulk_get_gmail_emails

```json
{
    "selected_server_credentials": {
        "MCP-GSUITE": {
            "credentials_path": "/path/to/google/credentials.json",
            "token_path": "/path/to/google/token.json"
        }
    },
    "client_details": {
        "api_key": "AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Use the bulk_get_gmail_emails tool to get multiple emails with IDs ['12345', '67890']",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "chat_model": "gemini-2.5-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["MCP-GSUITE"]
}
```

### 8. bulk_save_gmail_attachments

```json
{
    "selected_server_credentials": {
        "MCP-GSUITE": {
            "credentials_path": "/path/to/google/credentials.json",
            "token_path": "/path/to/google/token.json"
        }
    },
    "client_details": {
        "api_key": "AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Use the bulk_save_gmail_attachments tool to save attachments from emails with IDs ['12345', '67890'] to directory '/downloads'",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "chat_model": "gemini-2.5-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["MCP-GSUITE"]
}
```

### 9. send_gmail_email

```json
{
    "selected_server_credentials": {
        "MCP-GSUITE": {
            "credentials_path": "/path/to/google/credentials.json",
            "token_path": "/path/to/google/token.json"
        }
    },
    "client_details": {
        "api_key": "AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Use the send_gmail_email tool to send an email to '<EMAIL>' with subject 'Test Email' and body 'This is a test email'",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "chat_model": "gemini-2.5-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["MCP-GSUITE"]
}
```

---

## Calendar Tools

### 10. list_calendars

```json
{
    "selected_server_credentials": {
        "MCP-GSUITE": {
            "credentials_path": "/path/to/google/credentials.json",
            "token_path": "/path/to/google/token.json"
        }
    },
    "client_details": {
        "api_key": "AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Use the list_calendars tool to list all available calendars",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "chat_model": "gemini-2.5-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["MCP-GSUITE"]
}
```

### 11. get_calendar_events

```json
{
    "selected_server_credentials": {
        "MCP-GSUITE": {
            "credentials_path": "/path/to/google/credentials.json",
            "token_path": "/path/to/google/token.json"
        }
    },
    "client_details": {
        "api_key": "AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Use the get_calendar_events tool to get events from the primary calendar for the next 7 days",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "chat_model": "gemini-2.5-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["MCP-GSUITE"]
}
```

### 12. create_calendar_event

```json
{
    "selected_server_credentials": {
        "MCP-GSUITE": {
            "credentials_path": "/path/to/google/credentials.json",
            "token_path": "/path/to/google/token.json"
        }
    },
    "client_details": {
        "api_key": "AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Use the create_calendar_event tool to create a meeting titled 'Team Standup' on 2025-01-15 from 10:00 to 11:00",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "chat_model": "gemini-2.5-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["MCP-GSUITE"]
}
```

### 13. delete_calendar_event

```json
{
    "selected_server_credentials": {
        "MCP-GSUITE": {
            "credentials_path": "/path/to/google/credentials.json",
            "token_path": "/path/to/google/token.json"
        }
    },
    "client_details": {
        "api_key": "AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Use the delete_calendar_event tool to delete calendar event with ID 'event123'",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "chat_model": "gemini-2.5-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["MCP-GSUITE"]
}
```

### 14. check_calendar_availability

```json
{
    "selected_server_credentials": {
        "MCP-GSUITE": {
            "credentials_path": "/path/to/google/credentials.json",
            "token_path": "/path/to/google/token.json"
        }
    },
    "client_details": {
        "api_key": "AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Use the check_calendar_availability tool to check availability on 2025-01-15 from 14:00 to 16:00",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "chat_model": "gemini-2.5-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["MCP-GSUITE"]
}
```

---

## Google Meet Tools

### 15. create_meet_meeting

```json
{
    "selected_server_credentials": {
        "MCP-GSUITE": {
            "credentials_path": "/path/to/google/credentials.json",
            "token_path": "/path/to/google/token.json"
        }
    },
    "client_details": {
        "api_key": "AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Use the create_meet_meeting tool to create a Google Meet meeting for 'Project Review' on 2025-01-15 at 15:00",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "chat_model": "gemini-2.5-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["MCP-GSUITE"]
}
```

### 16. cancel_meet_meeting

```json
{
    "selected_server_credentials": {
        "MCP-GSUITE": {
            "credentials_path": "/path/to/google/credentials.json",
            "token_path": "/path/to/google/token.json"
        }
    },
    "client_details": {
        "api_key": "AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Use the cancel_meet_meeting tool to cancel Google Meet meeting with ID 'meeting123'",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "chat_model": "gemini-2.5-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["MCP-GSUITE"]
}
```

### 17. reschedule_meet_meeting

```json
{
    "selected_server_credentials": {
        "MCP-GSUITE": {
            "credentials_path": "/path/to/google/credentials.json",
            "token_path": "/path/to/google/token.json"
        }
    },
    "client_details": {
        "api_key": "AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Use the reschedule_meet_meeting tool to reschedule meeting with ID 'meeting123' to 2025-01-16 at 10:00",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "chat_model": "gemini-2.5-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["MCP-GSUITE"]
}
```

### 18. get_all_meet_meetings

```json
{
    "selected_server_credentials": {
        "MCP-GSUITE": {
            "credentials_path": "/path/to/google/credentials.json",
            "token_path": "/path/to/google/token.json"
        }
    },
    "client_details": {
        "api_key": "AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Use the get_all_meet_meetings tool to get all Google Meet meetings",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "chat_model": "gemini-2.5-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["MCP-GSUITE"]
}
```

---

## Summary

**Total Tools Available:**
- **MCP-JOOMLA**: 12 tools ✅ FULLY FUNCTIONAL
- **CLI-MCP-SERVER**: 9 tools ✅ EXECUTION PIPELINE WORKING
- **MCP-GSUITE**: 18 tools ✅ INITIALIZED AND READY

**All payloads use:**
- Model: `gemini-2.5-flash`
- API Key: `AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU`
- Client: `MCP_CLIENT_GEMINI`

**Status:** ✅ ALL 39 TOOLS DOCUMENTED AND READY FOR USE
