{"name": "ts-jest", "version": "29.2.6", "main": "dist/index.js", "types": "dist/index.d.ts", "bin": {"ts-jest": "cli.js"}, "description": "A Jest transformer with source map support that lets you use Jest to test projects written in TypeScript", "scripts": {"prebuild": "rimraf dist coverage *.tgz", "build": "tsc -p tsconfig.build.json", "postbuild": "node scripts/post-build.js", "test": "jest -c=jest-src.config.ts", "test-e2e-cjs": "jest -c=jest-e2e.config.cjs --no-cache", "test-e2e-esm": "node --experimental-vm-modules --no-warnings node_modules/jest/bin/jest.js -c=jest-e2e.config.mjs --no-cache", "test-examples": "node scripts/test-examples.js", "lint": "eslint --ext .js,.ts .", "lint-fix": "eslint --fix --ext .js,.ts .", "lint-prettier": "prettier \"**/*.{yml,yaml,md}\" --write", "lint-prettier-ci": "prettier '**/*.{yml,yaml,md}' --check", "doc": "cd website && npm run start", "doc:build": "cd website && npm run build", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s -r 1", "prepare": "npm run build", "prepublishOnly": "npm run test", "preversion": "npm run test", "version": "npm run changelog && git add CHANGELOG.md", "raw:options": "node scripts/generate-raw-compiler-options.js", "update-e2e": "node scripts/update-e2e.js"}, "repository": {"type": "git", "url": "git+https://github.com/kulshekhar/ts-jest.git"}, "keywords": ["jest", "typescript", "sourcemap", "react", "testing"], "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/kulshekhar)", "contributors": ["<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/huafu)", "<PERSON><PERSON> <<EMAIL>> (https://github.com/ahnpnl)", "<PERSON> <<EMAIL>> (https://github.com/GeeWee)"], "license": "MIT", "bugs": {"url": "https://github.com/kulshekhar/ts-jest/issues"}, "homepage": "https://kulshekhar.github.io/ts-jest", "dependencies": {"bs-logger": "^0.2.6", "ejs": "^3.1.10", "fast-json-stable-stringify": "^2.1.0", "jest-util": "^29.0.0", "json5": "^2.2.3", "lodash.memoize": "^4.1.2", "make-error": "^1.3.6", "semver": "^7.7.1", "yargs-parser": "^21.1.1"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.0 <8", "@jest/transform": "^29.0.0", "@jest/types": "^29.0.0", "babel-jest": "^29.0.0", "jest": "^29.0.0", "typescript": ">=4.3 <6"}, "peerDependenciesMeta": {"@babel/core": {"optional": true}, "@jest/transform": {"optional": true}, "@jest/types": {"optional": true}, "babel-jest": {"optional": true}, "esbuild": {"optional": true}}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "post-commit": "git reset"}}, "devDependencies": {"@commitlint/cli": "~18.6.1", "@commitlint/config-angular": "~18.6.1", "@jest/globals": "^29.7.0", "@jest/transform": "^29.7.0", "@jest/types": "^29.6.3", "@types/babel__core": "^7.20.5", "@types/ejs": "^3.1.5", "@types/fs-extra": "^11.0.4", "@types/jest": "^29.5.14", "@types/js-yaml": "^4.0.9", "@types/lodash.camelcase": "^4.3.9", "@types/lodash.memoize": "^4.1.9", "@types/lodash.set": "^4.3.9", "@types/micromatch": "^4.0.9", "@types/node": "20.17.19", "@types/semver": "^7.5.8", "@types/yargs": "^17.0.33", "@types/yargs-parser": "21.0.3", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "babel-jest": "^29.7.0", "conventional-changelog-cli": "^5.0.0", "esbuild": "~0.25.0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^28.11.0", "eslint-plugin-jsdoc": "^48.11.0", "eslint-plugin-prefer-arrow": "^1.2.3", "eslint-plugin-prettier": "^4.2.1", "execa": "5.1.1", "fs-extra": "^11.3.0", "glob": "^10.2.6", "glob-gitignore": "^1.0.15", "husky": "~4.3.8", "jest": "^29.7.0", "js-yaml": "^4.1.0", "json-schema-to-typescript": "^13.1.2", "lint-staged": "^15.4.3", "prettier": "^2.8.8", "typescript": "~5.5.4"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "git add"]}, "engines": {"node": "^14.15.0 || ^16.10.0 || ^18.0.0 || >=20.0.0"}}