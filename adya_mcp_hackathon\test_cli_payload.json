{"selected_client": "MCP_CLIENT_GEMINI", "selected_servers": ["CLI-MCP-SERVER"], "selected_server_credentials": {}, "client_details": {"api_key": "AIzaSyARTvccHRFHDYY2oqy9aHZDN4Z5dk-EJt4", "chat_model": "gemini-2.0-flash", "input": "Show me the security rules and then list the current directory", "prompt": "You are a helpful assistant that can execute CLI commands safely. Use the available tools to help users with system operations.", "is_stream": false}}