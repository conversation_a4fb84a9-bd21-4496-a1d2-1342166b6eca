# Complete Postman Testing Guide for MCP Servers

## 🚀 Prerequisites Setup

### 1. Import Postman Collection & Environment
1. **Import Collection**: Import `postman_api_collections/MCP.postman_collection.json`
2. **Import Environment**: Import `postman_api_collections/MCP.postman_environment.json`
3. **Set Environment**: Select "MCP" environment in Postman
4. **Verify Variables**: 
   - `dev-python-host` = `http://localhost:5001` (Your current server)
   - `dev-js-host` = `http://localhost:5000` (Not used for this testing)

### 2. Ensure Server is Running
- Your `run.py` should be running on `http://localhost:5001`
- Verify servers are loaded: MCP-JOOMLA (12 tools), CLI-MCP-SERVER (9 tools)

## 📋 Base API Configuration

### URL: `{{dev-python-host}}/api/v1/mcp/process_message`
### Method: `POST`
### Headers: `Content-Type: application/json`

## 🎯 Testing Strategy

### Base Payload Structure:
```json
{
    "selected_server_credentials": {
        // Server-specific credentials here
    },
    "client_details": {
        "api_key": "test-key-not-required-for-local",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "YOUR_TEST_COMMAND_HERE",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "chat_model": "gpt-4o",
        "chat_history": [
            {
                "role": "user",
                "content": "Hello"
            }
        ]
    },
    "selected_client": "MCP_CLIENT_OPENAI",
    "selected_servers": ["SERVER_NAME_HERE"]
}
```

## 🔧 MCP-JOOMLA Server Testing (12 Tools)

### Server Credentials for MCP-JOOMLA:
```json
"selected_server_credentials": {
    "MCP-JOOMLA": {
        "base_url": "https://ashhs.joomla.com/",
        "bearer_token": "c2hhMjU2Ojg4NDplNzQxYTM0MzQ3NjQ0ZDYzNWE3NTVjNmYwN2JkNTk3YjIwZjM3M2U5MjIzYzI3MzNkNTE1NTJhZjBjNDRlMWFj"
    }
},
"selected_servers": ["MCP-JOOMLA"]
```

### Test Cases for MCP-JOOMLA:

#### 1. Get All Articles
```json
"input": "Get all articles from Joomla"
```

#### 2. Get Article by ID
```json
"input": "Get article with ID 1 from Joomla"
```

#### 3. Search Articles
```json
"input": "Search for articles containing 'welcome' in Joomla"
```

#### 4. Get Categories
```json
"input": "Get all categories from Joomla"
```

#### 5. Create Article
```json
"input": "Create a new article titled 'Test Article' with content 'This is a test article created via MCP' in Joomla"
```

#### 6. Update Article
```json
"input": "Update article ID 1 with new title 'Updated Article Title' in Joomla"
```

#### 7. Manage Article State
```json
"input": "Publish article ID 1 in Joomla"
```

#### 8. Move Article to Trash
```json
"input": "Move article ID 1 to trash in Joomla"
```

#### 9. Get All Users
```json
"input": "Get all users from Joomla with limit 10"
```

#### 10. Get User by ID
```json
"input": "Get user details for user ID 1 from Joomla"
```

#### 11. Get Menus
```json
"input": "Get all menu structures from Joomla"
```

#### 12. Get Site Info
```json
"input": "Get general site information from Joomla"
```

## 🖥️ CLI-MCP-SERVER Testing (9 Tools)

### Server Credentials for CLI-MCP-SERVER:
```json
"selected_server_credentials": {},
"selected_servers": ["CLI-MCP-SERVER"]
```
*Note: CLI-MCP-SERVER doesn't require credentials as it uses WSL environment*

### Test Cases for CLI-MCP-SERVER:

#### 1. Run Command
```json
"input": "Execute 'ls -la' command in the allowed directory"
```

#### 2. Show Security Rules
```json
"input": "Show me the security rules and configuration for CLI commands"
```

#### 3. List Directory
```json
"input": "List the contents of current directory with detailed information including hidden files"
```

#### 4. Read File
```json
"input": "Read the contents of a file named 'test.txt' with line numbers"
```

#### 5. Search Files
```json
"input": "Search for files with name pattern 'test' in current directory"
```

#### 6. Get File Info
```json
"input": "Get detailed information about file 'test.txt'"
```

#### 7. Create Directory
```json
"input": "Create a new directory named 'test_directory' with parent directories if needed"
```

#### 8. Write File
```json
"input": "Write 'Hello World from MCP!' to a file named 'hello.txt'"
```

#### 9. System Info
```json
"input": "Get system information including OS details, disk usage, and memory usage"
```

## 📝 Step-by-Step Testing Process

### For Each Test:

1. **Open Postman**
2. **Select the MCP Environment**
3. **Create New Request**:
   - Method: `POST`
   - URL: `{{dev-python-host}}/api/v1/mcp/process_message`
   - Headers: `Content-Type: application/json`

4. **Copy Base Payload** and modify:
   - Update `selected_server_credentials` for the target server
   - Update `selected_servers` array
   - Update `input` field with specific test command

5. **Send Request**
6. **Verify Response**:
   - Check for successful execution
   - Verify tool was called correctly
   - Check response data format

## 🔍 Expected Response Format

Successful responses should contain:
```json
{
    "status": "success",
    "data": {
        "response": "Tool execution result...",
        "tool_calls": [...],
        "execution_time": "...",
        // Additional response data
    }
}
```

## 🚨 Troubleshooting

### Common Issues:

1. **Server Not Running**: Ensure `run.py` is active on port 5001
2. **Invalid Credentials**: Check Joomla site URL and bearer token
3. **Tool Not Found**: Verify server name matches exactly: "MCP-JOOMLA" or "CLI-MCP-SERVER"
4. **Permission Denied**: For CLI-MCP-SERVER, ensure commands are in allowed list
5. **Timeout**: Some operations may take time, especially file operations

### Debug Steps:
1. Check server logs in terminal running `run.py`
2. Verify server initialization shows all tools loaded
3. Test with simple commands first (like "show security rules")
4. Ensure environment variables are set correctly

## 📊 Testing Checklist

### MCP-JOOMLA (12/12 tools):
- [ ] get_joomla_articles
- [ ] get_article_by_id  
- [ ] search_articles
- [ ] get_joomla_categories
- [ ] create_article
- [ ] update_article
- [ ] manage_article_state
- [ ] move_article_to_trash
- [ ] get_joomla_users
- [ ] get_user_by_id
- [ ] get_joomla_menus
- [ ] get_site_info

### CLI-MCP-SERVER (9/9 tools):
- [ ] run_command
- [ ] show_security_rules
- [ ] list_directory
- [ ] read_file
- [ ] search_files
- [ ] get_file_info
- [ ] create_directory
- [ ] write_file
- [ ] system_info

## 🎉 Success Criteria

- All 21 tools (12 + 9) should execute without errors
- Each tool should return appropriate response data
- Server logs should show successful tool execution
- No security violations for CLI-MCP-SERVER commands
