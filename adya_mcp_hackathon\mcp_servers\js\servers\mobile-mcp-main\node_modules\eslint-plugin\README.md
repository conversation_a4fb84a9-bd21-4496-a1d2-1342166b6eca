# eslint-plugin-saas

测试组织包

## Installation

You'll first need to install [ESLint](http://eslint.org):

```
$ npm i eslint --save-dev
```

Next, install `eslint-plugin-saas`:

```
$ npm install eslint-plugin-saas --save-dev
```


## Usage

Add `saas` to the plugins section of your `.eslintrc` configuration file. You can omit the `eslint-plugin-` prefix:

```json
{
    "plugins": [
        "saas"
    ]
}
```


Then configure the rules you want to use under the rules section.

```json
{
    "rules": {
        "saas/rule-name": 2
    }
}
```

## Supported Rules

* Fill in provided rules here





