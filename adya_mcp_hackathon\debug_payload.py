#!/usr/bin/env python3

import json

# Test CLI-MCP-SERVER payload
cli_payload = {
    "selected_server_credentials": {},
    "client_details": {
        "api_key": "AIzaSyARTvccHRFHDYY2oqy9aHZDN4Z5dk-EJt4",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "List the contents of the current directory",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "chat_model": "gemini-2.0-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["CLI-MCP-SERVER"]
}

print("CLI-MCP-SERVER Payload:")
print(json.dumps(cli_payload, indent=2))

# Check validation conditions
selected_server_credentials = cli_payload.get("selected_server_credentials")
client_details = cli_payload.get("client_details", {})
selected_client = cli_payload.get("selected_client", "")
selected_servers = cli_payload.get("selected_servers", [])

print(f"\nValidation Check:")
print(f"selected_client: '{selected_client}' -> bool: {bool(selected_client)}")
print(f"selected_servers: {selected_servers} -> bool: {bool(selected_servers)}")
print(f"selected_server_credentials: {selected_server_credentials} -> is None: {selected_server_credentials is None}")
print(f"client_details: {list(client_details.keys())} -> bool: {bool(client_details)}")

# Check the validation condition
validation_fails = not selected_client or not selected_servers or selected_server_credentials is None or not client_details
print(f"\nValidation fails: {validation_fails}")

if validation_fails:
    print("❌ Validation would fail")
else:
    print("✅ Validation would pass")
