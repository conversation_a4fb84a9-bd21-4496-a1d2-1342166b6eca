#!/usr/bin/env python3

import asyncio
import json
import aiohttp
import sys

async def test_mcp_server():
    """Test the MCP server endpoints"""
    
    # Test payloads
    joomla_payload = {
        "selected_server_credentials": {
            "MCP-JOOMLA": {
                "base_url": "https://ashhs.joomla.com/",
                "bearer_token": "c2hhMjU2Ojg4NDplNzQxYTM0MzQ3NjQ0ZDYzNWE3NTVjNmYwN2JkNTk3YjIwZjM3M2U5MjIzYzI3MzNkNTE1NTJhZjBjNDRlMWFj"
            }
        },
        "client_details": {
            "api_key": "AIzaSyARTvccHRFHDYY2oqy9aHZDN4Z5dk-EJt4",
            "temperature": 0.1,
            "max_token": 20000,
            "input": "Get all categories from Joom<PERSON>",
            "input_type": "text",
            "prompt": "you are a helpful assistant",
            "chat_model": "gemini-2.0-flash",
            "chat_history": [{"role": "user", "content": "Hello"}]
        },
        "selected_client": "MCP_CLIENT_GEMINI",
        "selected_servers": ["MCP-JOOMLA"]
    }
    
    cli_payload = {
        "selected_server_credentials": {},
        "client_details": {
            "api_key": "AIzaSyARTvccHRFHDYY2oqy9aHZDN4Z5dk-EJt4",
            "temperature": 0.1,
            "max_token": 20000,
            "input": "List the contents of the current directory",
            "input_type": "text",
            "prompt": "you are a helpful assistant",
            "chat_model": "gemini-2.0-flash",
            "chat_history": [{"role": "user", "content": "Hello"}]
        },
        "selected_client": "MCP_CLIENT_GEMINI",
        "selected_servers": ["CLI-MCP-SERVER"]
    }
    
    url = "http://localhost:5001/api/v1/mcp/process_message"
    
    async with aiohttp.ClientSession() as session:
        print("🧪 Testing MCP-JOOMLA...")
        try:
            async with session.post(url, json=joomla_payload) as response:
                result = await response.json()
                print(f"Status: {response.status}")
                print(f"Response: {json.dumps(result, indent=2)}")
        except Exception as e:
            print(f"❌ MCP-JOOMLA test failed: {e}")
        
        print("\n" + "="*50 + "\n")
        
        print("🧪 Testing CLI-MCP-SERVER...")
        try:
            async with session.post(url, json=cli_payload) as response:
                result = await response.json()
                print(f"Status: {response.status}")
                print(f"Response: {json.dumps(result, indent=2)}")
        except Exception as e:
            print(f"❌ CLI-MCP-SERVER test failed: {e}")

if __name__ == "__main__":
    try:
        asyncio.run(test_mcp_server())
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
    except Exception as e:
        print(f"❌ Test failed: {e}")
        sys.exit(1)
