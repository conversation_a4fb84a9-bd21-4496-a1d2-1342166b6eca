#!/usr/bin/env python3

import asyncio
import sys
import os
import json

# Add the clients directory to the path
sys.path.append('mcp_servers/python/clients')

async def debug_validation():
    try:
        print("🔍 Debugging validation...")
        
        # CLI-MCP-SERVER payload (same as PowerShell test)
        cli_data = {
            "selected_server_credentials": {},
            "client_details": {
                "api_key": "AIzaSyARTvccHRFHDYY2oqy9aHZDN4Z5dk-EJt4",
                "temperature": 0.1,
                "max_token": 20000,
                "input": "List the contents of the current directory",
                "input_type": "text",
                "prompt": "you are a helpful assistant",
                "chat_model": "gemini-2.0-flash",
                "chat_history": [{"role": "user", "content": "Hello"}],
                "is_stream": False
            },
            "selected_client": "MCP_CLIENT_GEMINI",
            "selected_servers": ["CLI-MCP-SERVER"]
        }
        
        print(f"📦 CLI Payload: {json.dumps(cli_data, indent=2)}")
        
        # Initialize MCP servers first
        from src.server_connection import initialize_all_mcp
        from contextlib import AsyncExitStack
        
        exit_stack = AsyncExitStack()
        await exit_stack.__aenter__()
        
        try:
            print("🚀 Initializing MCP servers...")
            await initialize_all_mcp(exit_stack)
            print("✅ MCP servers initialized")
            
            # Test validation with detailed debugging
            print("🔍 Testing CLI validation...")
            from src.client_and_server_validation import client_and_server_validation
            
            # Extract the exact same way as the server does
            selected_client = cli_data.get("selected_client")
            selected_servers = cli_data.get("selected_servers")
            selected_server_credentials = cli_data.get("selected_server_credentials")
            client_details = cli_data.get("client_details")
            
            print(f"🔍 selected_client: {selected_client} (type: {type(selected_client)})")
            print(f"🔍 selected_servers: {selected_servers} (type: {type(selected_servers)})")
            print(f"🔍 selected_server_credentials: {selected_server_credentials} (type: {type(selected_server_credentials)})")
            print(f"🔍 client_details: {client_details is not None} (type: {type(client_details)})")
            
            # Check each condition
            print(f"🔍 not selected_client: {not selected_client}")
            print(f"🔍 not selected_servers: {not selected_servers}")
            print(f"🔍 selected_server_credentials is None: {selected_server_credentials is None}")
            print(f"🔍 not client_details: {not client_details}")
            
            validation_result = await client_and_server_validation(cli_data, {"streamCallbacks": None, "is_stream": False})
            print(f"✅ CLI Validation result: {validation_result['status']}")
            if not validation_result['status']:
                print(f"❌ CLI Validation error: {validation_result['error']}")
            
            # Now test MCP-JOOMLA for comparison
            joomla_data = {
                "selected_server_credentials": {
                    "MCP-JOOMLA": {
                        "base_url": "https://joomla.adyawinsa.com",
                        "bearer_token": "ZWQ5YzJhNzEtNzNhNy00YzNhLWI5YzMtNzNhN2M5YzJhNzE6ZWQ5YzJhNzEtNzNhNy00YzNhLWI5YzMtNzNhN2M5YzJhNzE="
                    }
                },
                "client_details": {
                    "api_key": "AIzaSyARTvccHRFHDYY2oqy9aHZDN4Z5dk-EJt4",
                    "temperature": 0.1,
                    "max_token": 20000,
                    "input": "Get all Joomla categories",
                    "input_type": "text",
                    "prompt": "you are a helpful assistant",
                    "chat_model": "gemini-2.0-flash",
                    "chat_history": [{"role": "user", "content": "Hello"}],
                    "is_stream": False
                },
                "selected_client": "MCP_CLIENT_GEMINI",
                "selected_servers": ["MCP-JOOMLA"]
            }
            
            print("\n🔍 Testing JOOMLA validation...")
            validation_result = await client_and_server_validation(joomla_data, {"streamCallbacks": None, "is_stream": False})
            print(f"✅ JOOMLA Validation result: {validation_result['status']}")
            if not validation_result['status']:
                print(f"❌ JOOMLA Validation error: {validation_result['error']}")
                
        finally:
            await exit_stack.__aexit__(None, None, None)
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_validation())
