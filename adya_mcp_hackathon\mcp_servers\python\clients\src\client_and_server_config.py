ClientsConfig =[
    "MCP_CLIENT_AZURE_AI",
    "MCP_CLIENT_OPENAI",
	"MCP_CLIENT_GEMINI"
]
ServersConfig = [
	{
		"server_name": "MCP-GSUITE",
		"command":"uv",
		"args": [
			"--directory",
			"../servers/MCP-GSUITE/mcp-gsuite",
			"run",
			"mcp-gsuite"
		]
	},
	{
		"server_name": "MCP-JOOMLA",
		"command":"uv",
		"args": [
			"--directory",
			"../servers/MCP-JOOMLA/mcp-joomla",
			"run",
			"mcp-joomla"
		]
	},
	{
		"server_name": "CLI-MCP-SERVER",
		"command": "uv",
		"args": [
			"--directory",
			"../servers/cli-mcp-server",
			"run",
			"cli-mcp-server"
		],
		"env": {
			"ALLOWED_DIR": "D:\\Users 2.o\\PY charm\\adya_hackathon\\test_mcp_directory",
			"ALLOWED_COMMANDS": "ls,cat,pwd,echo,find,grep,stat,mkdir,df,free,uname,head,tail,sed",
			"ALLOWED_FLAGS": "-l,-a,--help,--version,-h,-r,-n,-p,-maxdepth,-name,-type,-f",
			"MAX_COMMAND_LENGTH": "2048",
			"COMMAND_TIMEOUT": "60",
			"ALLOW_SHELL_OPERATORS": "true"
		}
	},
	{
		"server_name": "MOBILE_MCP",
		"command": "node",
		"args": [
			"../../js/servers/mobile-mcp-main/lib/index.js"
		]
	},
	{
		"server_name": "EVENTBRITE_MCP",
		"command": "node",
		"args": [
			"../../js/servers/eventbrite-mcp-main/build/index.js"
		],
		"env": {
			"EVENTBRITE_API_KEY": "YOUR_EVENTBRITE_API_KEY"
		}
	}
]