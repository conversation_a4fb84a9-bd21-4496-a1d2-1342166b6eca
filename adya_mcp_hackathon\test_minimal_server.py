#!/usr/bin/env python3

import asyncio
import sys
import os

# Add the clients directory to the path
sys.path.append('mcp_servers/python/clients')

async def test_minimal_server():
    try:
        print("🔍 Testing minimal server...")
        
        from quart import Quart, request, jsonify
        from hypercorn.asyncio import serve
        from hypercorn.config import Config
        
        app = Quart(__name__)
        
        @app.route("/health", methods=["GET"])
        async def health_check():
            print("🏥 Health check endpoint called!")
            return {"status": "healthy", "message": "Server is running"}
        
        @app.route("/api/v1/mcp/process_message", methods=["POST"])
        async def process_message():
            print("📨 Process message endpoint called!")
            try:
                data = await request.get_json()
                print(f"📦 Received data keys: {list(data.keys()) if data else 'None'}")
                
                return jsonify({
                    "Data": {"message": "Test response"},
                    "Error": None,
                    "Status": True
                }), 200
                
            except Exception as e:
                print(f"❌ Error in endpoint: {e}")
                return jsonify({
                    "Data": None,
                    "Error": str(e),
                    "Status": False
                }), 500
        
        config = Config()
        config.bind = ["0.0.0.0:5001"]
        
        print("🌟 Starting minimal server on http://0.0.0.0:5001")
        print("📡 Endpoints available:")
        print("  - GET  /health")
        print("  - POST /api/v1/mcp/process_message")
        
        await serve(app, config)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_minimal_server())
