Stack trace:
Frame         Function      Args
0007FFFFA0F0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF8FF0) msys-2.0.dll+0x1FE8E
0007FFFFA0F0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA3C8) msys-2.0.dll+0x67F9
0007FFFFA0F0  000210046832 (000210286019, 0007FFFF9FA8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFA0F0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFA0F0  000210068E24 (0007FFFFA100, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA3D0  00021006A225 (0007FFFFA100, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFBE5BC0000 ntdll.dll
7FFBE4230000 KERNEL32.DLL
7FFBE2D10000 KERNELBASE.dll
7FFBE56F0000 USER32.dll
7FFBE3710000 win32u.dll
000210040000 msys-2.0.dll
7FFBE55C0000 GDI32.dll
7FFBE31C0000 gdi32full.dll
7FFBE3390000 msvcp_win.dll
7FFBE3440000 ucrtbase.dll
7FFBE3960000 advapi32.dll
7FFBE4C30000 msvcrt.dll
7FFBE58E0000 sechost.dll
7FFBE4300000 RPCRT4.dll
7FFBE22F0000 CRYPTBASE.DLL
7FFBE3740000 bcryptPrimitives.dll
7FFBE4420000 IMM32.DLL
