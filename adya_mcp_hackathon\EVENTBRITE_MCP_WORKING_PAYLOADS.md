# Eventbrite MCP Working Payloads - Gemini 2.5 Flash

## Overview
Focused payload documentation for Eventbrite MCP tools that will work with your current authentication setup.

**API Configuration:**
- Model: `gemini-2.5-flash`
- API Key: `AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU`
- Client: `MCP_CLIENT_GEMINI`
- Eventbrite Private Token: `********************`

**⚠️ AUTHENTICATION NOTE:** Your current token appears to be a private token. Some tools may require OAuth authentication for full functionality.

---

## ✅ RECOMMENDED FIRST TESTS - Basic API Tools

### 1. get_categories ⭐ BEST FIRST TEST

```json
{
    "selected_server_credentials": {
        "EVENTBRITE_MCP": {
            "api_key": "********************"
        }
    },
    "client_details": {
        "api_key": "AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Use the get_categories tool to get a list of all Eventbrite event categories",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "chat_model": "gemini-2.5-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["EVENTBRITE_MCP"]
}
```

### 2. get_user_profile ⭐ AUTHENTICATION TEST

```json
{
    "selected_server_credentials": {
        "EVENTBRITE_MCP": {
            "api_key": "********************"
        }
    },
    "client_details": {
        "api_key": "AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Use the get_user_profile tool to get my Eventbrite user profile and account information",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "chat_model": "gemini-2.5-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["EVENTBRITE_MCP"]
}
```

---

## ⚠️ YOUR ACCOUNT TOOLS - Requires Your Events/Organization

### 3. get_organization_events

```json
{
    "selected_server_credentials": {
        "EVENTBRITE_MCP": {
            "api_key": "********************"
        }
    },
    "client_details": {
        "api_key": "AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Use the get_organization_events tool to get events from my organization with status 'live'",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "chat_model": "gemini-2.5-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["EVENTBRITE_MCP"]
}
```

### 4. get_event (Requires Valid Event ID)

```json
{
    "selected_server_credentials": {
        "EVENTBRITE_MCP": {
            "api_key": "********************"
        }
    },
    "client_details": {
        "api_key": "AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Use the get_event tool to get detailed information about event with event_id 'YOUR_EVENT_ID'",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "chat_model": "gemini-2.5-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["EVENTBRITE_MCP"]
}
```

### 5. get_venue (Requires Valid Venue ID)

```json
{
    "selected_server_credentials": {
        "EVENTBRITE_MCP": {
            "api_key": "********************"
        }
    },
    "client_details": {
        "api_key": "AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Use the get_venue tool to get information about venue with venue_id 'YOUR_VENUE_ID'",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "chat_model": "gemini-2.5-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["EVENTBRITE_MCP"]
}
```

### 6. get_event_attendees_summary (Requires Your Event ID)

```json
{
    "selected_server_credentials": {
        "EVENTBRITE_MCP": {
            "api_key": "********************"
        }
    },
    "client_details": {
        "api_key": "AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Use the get_event_attendees_summary tool to get attendee summary for my event with event_id 'YOUR_EVENT_ID'",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "chat_model": "gemini-2.5-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["EVENTBRITE_MCP"]
}
```

### 7. get_event_ticket_classes (Requires Your Event ID)

```json
{
    "selected_server_credentials": {
        "EVENTBRITE_MCP": {
            "api_key": "********************"
        }
    },
    "client_details": {
        "api_key": "AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Use the get_event_ticket_classes tool to get ticket classes for my event with event_id 'YOUR_EVENT_ID'",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "chat_model": "gemini-2.5-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["EVENTBRITE_MCP"]
}
```

### 8. get_event_orders (Requires Your Event ID)

```json
{
    "selected_server_credentials": {
        "EVENTBRITE_MCP": {
            "api_key": "********************"
        }
    },
    "client_details": {
        "api_key": "AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Use the get_event_orders tool to get orders for my event with event_id 'YOUR_EVENT_ID' and status 'placed'",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "chat_model": "gemini-2.5-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["EVENTBRITE_MCP"]
}
```

### 9. get_event_discounts (Requires Your Event ID)

```json
{
    "selected_server_credentials": {
        "EVENTBRITE_MCP": {
            "api_key": "********************"
        }
    },
    "client_details": {
        "api_key": "AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Use the get_event_discounts tool to get discount codes for my event with event_id 'YOUR_EVENT_ID'",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "chat_model": "gemini-2.5-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["EVENTBRITE_MCP"]
}
```

---

## ❌ PROBLEMATIC TOOLS - Likely to Fail with Current Setup

### 10. search_events (Public Search - OAuth Issues)

```json
{
    "selected_server_credentials": {
        "EVENTBRITE_MCP": {
            "api_key": "********************"
        }
    },
    "client_details": {
        "api_key": "AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Use the search_events tool to search for events with query 'tech conference'",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "chat_model": "gemini-2.5-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["EVENTBRITE_MCP"]
}
```

---

## 🎯 TESTING STRATEGY

### Step 1: Test Basic API Access
1. **Start with `get_categories`** - This should work with any valid API key
2. **Test `get_user_profile`** - This will confirm your authentication is working

### Step 2: Test Your Account Data
3. **Try `get_organization_events`** - This will show if you have any events
4. **Use event IDs from step 3** to test other event-specific tools

### Step 3: Avoid for Now
- **`search_events`** - Requires different OAuth setup for public event search

**Status:** ✅ ALL 10 TOOLS DOCUMENTED WITH WORKING API KEY
