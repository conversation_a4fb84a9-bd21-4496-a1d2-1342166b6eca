# Updated MCP-JO<PERSON><PERSON> Payloads with Working Format

## Working Format Template
All payloads use the new Gemini API key: `AIzaSyDZsa-ifofUJuYsLu6-R5_0pxmrkB_L7fQ`
All payloads include credentials in both `selected_server_credentials` and `client_details` sections.

## Status Legend
✅ VERIFIED WORKING - <PERSON><PERSON> tested and confirmed working
❌ HAS BUG - Tool executes but has implementation issues
🔄 NEEDS TESTING - Tool not yet tested with new format

---

## 1. Get All Articles ✅ VERIFIED WORKING
```json
{
    "selected_server_credentials": {
        "MCP-JOOMLA": {
            "base_url": "https://ashhs.joomla.com/",
            "bearer_token": "c2hhMjU2Ojg4NDplNzQxYTM0MzQ3NjQ0ZDYzNWE3NTVjNmYwN2JkNTk3YjIwZjM3M2U5MjIzYzI3MzNkNTE1NTJhZjBjNDRlMWFj"
        }
    },
    "client_details": {
        "api_key": "AIzaSyDZsa-ifofUJuYsLu6-R5_0pxmrkB_L7fQ",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Get all articles from Joomla base_url: https://ashhs.joomla.com/, bearer_token: c2hhMjU2Ojg4NDplNzQxYTM0MzQ3NjQ0ZDYzNWE3NTVjNmYwN2JkNTk3YjIwZjM3M2U5MjIzYzI3MzNkNTE1NTJhZjBjNDRlMWFj",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "base_url": "https://ashhs.joomla.com/",
        "bearer_token": "c2hhMjU2Ojg4NDplNzQxYTM0MzQ3NjQ0ZDYzNWE3NTVjNmYwN2JkNTk3YjIwZjM3M2U5MjIzYzI3MzNkNTE1NTJhZjBjNDRlMWFj",
        "chat_model": "gemini-2.0-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["MCP-JOOMLA"]
}
```

## 2. Get Categories ✅ VERIFIED WORKING
```json
{
    "selected_server_credentials": {
        "MCP-JOOMLA": {
            "base_url": "https://ashhs.joomla.com/",
            "bearer_token": "c2hhMjU2Ojg4NDplNzQxYTM0MzQ3NjQ0ZDYzNWE3NTVjNmYwN2JkNTk3YjIwZjM3M2U5MjIzYzI3MzNkNTE1NTJhZjBjNDRlMWFj"
        }
    },
    "client_details": {
        "api_key": "AIzaSyDZsa-ifofUJuYsLu6-R5_0pxmrkB_L7fQ",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Get all categories from Joomla base_url: https://ashhs.joomla.com/, bearer_token: c2hhMjU2Ojg4NDplNzQxYTM0MzQ3NjQ0ZDYzNWE3NTVjNmYwN2JkNTk3YjIwZjM3M2U5MjIzYzI3MzNkNTE1NTJhZjBjNDRlMWFj",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "base_url": "https://ashhs.joomla.com/",
        "bearer_token": "c2hhMjU2Ojg4NDplNzQxYTM0MzQ3NjQ0ZDYzNWE3NTVjNmYwN2JkNTk3YjIwZjM3M2U5MjIzYzI3MzNkNTE1NTJhZjBjNDRlMWFj",
        "chat_model": "gemini-2.0-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["MCP-JOOMLA"]
}
```

## 3. Get Users ✅ VERIFIED WORKING
```json
{
    "selected_server_credentials": {
        "MCP-JOOMLA": {
            "base_url": "https://ashhs.joomla.com/",
            "bearer_token": "c2hhMjU2Ojg4NDplNzQxYTM0MzQ3NjQ0ZDYzNWE3NTVjNmYwN2JkNTk3YjIwZjM3M2U5MjIzYzI3MzNkNTE1NTJhZjBjNDRlMWFj"
        }
    },
    "client_details": {
        "api_key": "AIzaSyDZsa-ifofUJuYsLu6-R5_0pxmrkB_L7fQ",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Get all users from Joomla with limit 10 base_url: https://ashhs.joomla.com/, bearer_token: c2hhMjU2Ojg4NDplNzQxYTM0MzQ3NjQ0ZDYzNWE3NTVjNmYwN2JkNTk3YjIwZjM3M2U5MjIzYzI3MzNkNTE1NTJhZjBjNDRlMWFj",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "base_url": "https://ashhs.joomla.com/",
        "bearer_token": "c2hhMjU2Ojg4NDplNzQxYTM0MzQ3NjQ0ZDYzNWE3NTVjNmYwN2JkNTk3YjIwZjM3M2U5MjIzYzI3MzNkNTE1NTJhZjBjNDRlMWFj",
        "chat_model": "gemini-2.0-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["MCP-JOOMLA"]
}
```

## 4. Get Site Info ❌ HAS BUG - "'list' object has no attribute 'get'"
```json
{
    "selected_server_credentials": {
        "MCP-JOOMLA": {
            "base_url": "https://ashhs.joomla.com/",
            "bearer_token": "c2hhMjU2Ojg4NDplNzQxYTM0MzQ3NjQ0ZDYzNWE3NTVjNmYwN2JkNTk3YjIwZjM3M2U5MjIzYzI3MzNkNTE1NTJhZjBjNDRlMWFj"
        }
    },
    "client_details": {
        "api_key": "AIzaSyDZsa-ifofUJuYsLu6-R5_0pxmrkB_L7fQ",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Get site information from Joomla base_url: https://ashhs.joomla.com/, bearer_token: c2hhMjU2Ojg4NDplNzQxYTM0MzQ3NjQ0ZDYzNWE3NTVjNmYwN2JkNTk3YjIwZjM3M2U5MjIzYzI3MzNkNTE1NTJhZjBjNDRlMWFj",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "base_url": "https://ashhs.joomla.com/",
        "bearer_token": "c2hhMjU2Ojg4NDplNzQxYTM0MzQ3NjQ0ZDYzNWE3NTVjNmYwN2JkNTk3YjIwZjM3M2U5MjIzYzI3MzNkNTE1NTJhZjBjNDRlMWFj",
        "chat_model": "gemini-2.0-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["MCP-JOOMLA"]
}
```

## 5. Create Article 🔄 NEEDS TESTING
```json
{
    "selected_server_credentials": {
        "MCP-JOOMLA": {
            "base_url": "https://ashhs.joomla.com/",
            "bearer_token": "c2hhMjU2Ojg4NDplNzQxYTM0MzQ3NjQ0ZDYzNWE3NTVjNmYwN2JkNTk3YjIwZjM3M2U5MjIzYzI3MzNkNTE1NTJhZjBjNDRlMWFj"
        }
    },
    "client_details": {
        "api_key": "AIzaSyDZsa-ifofUJuYsLu6-R5_0pxmrkB_L7fQ",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Create a new article in Joomla with title 'Test Article' and content 'This is a test article created via MCP' base_url: https://ashhs.joomla.com/, bearer_token: c2hhMjU2Ojg4NDplNzQxYTM0MzQ3NjQ0ZDYzNWE3NTVjNmYwN2JkNTk3YjIwZjM3M2U5MjIzYzI3MzNkNTE1NTJhZjBjNDRlMWFj",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "base_url": "https://ashhs.joomla.com/",
        "bearer_token": "c2hhMjU2Ojg4NDplNzQxYTM0MzQ3NjQ0ZDYzNWE3NTVjNmYwN2JkNTk3YjIwZjM3M2U5MjIzYzI3MzNkNTE1NTJhZjBjNDRlMWFj",
        "chat_model": "gemini-2.0-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["MCP-JOOMLA"]
}
```

## 6. Get Article By ID 🔄 NEEDS TESTING
```json
{
    "selected_server_credentials": {
        "MCP-JOOMLA": {
            "base_url": "https://ashhs.joomla.com/",
            "bearer_token": "c2hhMjU2Ojg4NDplNzQxYTM0MzQ3NjQ0ZDYzNWE3NTVjNmYwN2JkNTk3YjIwZjM3M2U5MjIzYzI3MzNkNTE1NTJhZjBjNDRlMWFj"
        }
    },
    "client_details": {
        "api_key": "AIzaSyDZsa-ifofUJuYsLu6-R5_0pxmrkB_L7fQ",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Get article with ID 1 from Joomla base_url: https://ashhs.joomla.com/, bearer_token: c2hhMjU2Ojg4NDplNzQxYTM0MzQ3NjQ0ZDYzNWE3NTVjNmYwN2JkNTk3YjIwZjM3M2U5MjIzYzI3MzNkNTE1NTJhZjBjNDRlMWFj",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "base_url": "https://ashhs.joomla.com/",
        "bearer_token": "c2hhMjU2Ojg4NDplNzQxYTM0MzQ3NjQ0ZDYzNWE3NTVjNmYwN2JkNTk3YjIwZjM3M2U5MjIzYzI3MzNkNTE1NTJhZjBjNDRlMWFj",
        "chat_model": "gemini-2.0-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["MCP-JOOMLA"]
}
```

## 7. Search Articles 🔄 NEEDS TESTING
```json
{
    "selected_server_credentials": {
        "MCP-JOOMLA": {
            "base_url": "https://ashhs.joomla.com/",
            "bearer_token": "c2hhMjU2Ojg4NDplNzQxYTM0MzQ3NjQ0ZDYzNWE3NTVjNmYwN2JkNTk3YjIwZjM3M2U5MjIzYzI3MzNkNTE1NTJhZjBjNDRlMWFj"
        }
    },
    "client_details": {
        "api_key": "AIzaSyDZsa-ifofUJuYsLu6-R5_0pxmrkB_L7fQ",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Search for articles containing 'test' in Joomla base_url: https://ashhs.joomla.com/, bearer_token: c2hhMjU2Ojg4NDplNzQxYTM0MzQ3NjQ0ZDYzNWE3NTVjNmYwN2JkNTk3YjIwZjM3M2U5MjIzYzI3MzNkNTE1NTJhZjBjNDRlMWFj",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "base_url": "https://ashhs.joomla.com/",
        "bearer_token": "c2hhMjU2Ojg4NDplNzQxYTM0MzQ3NjQ0ZDYzNWE3NTVjNmYwN2JkNTk3YjIwZjM3M2U5MjIzYzI3MzNkNTE1NTJhZjBjNDRlMWFj",
        "chat_model": "gemini-2.0-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["MCP-JOOMLA"]
}
```

## 8. Manage Article State 🔄 NEEDS TESTING
```json
{
    "selected_server_credentials": {
        "MCP-JOOMLA": {
            "base_url": "https://ashhs.joomla.com/",
            "bearer_token": "c2hhMjU2Ojg4NDplNzQxYTM0MzQ3NjQ0ZDYzNWE3NTVjNmYwN2JkNTk3YjIwZjM3M2U5MjIzYzI3MzNkNTE1NTJhZjBjNDRlMWFj"
        }
    },
    "client_details": {
        "api_key": "AIzaSyDZsa-ifofUJuYsLu6-R5_0pxmrkB_L7fQ",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Publish article with ID 1 in Joomla base_url: https://ashhs.joomla.com/, bearer_token: c2hhMjU2Ojg4NDplNzQxYTM0MzQ3NjQ0ZDYzNWE3NTVjNmYwN2JkNTk3YjIwZjM3M2U5MjIzYzI3MzNkNTE1NTJhZjBjNDRlMWFj",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "base_url": "https://ashhs.joomla.com/",
        "bearer_token": "c2hhMjU2Ojg4NDplNzQxYTM0MzQ3NjQ0ZDYzNWE3NTVjNmYwN2JkNTk3YjIwZjM3M2U5MjIzYzI3MzNkNTE1NTJhZjBjNDRlMWFj",
        "chat_model": "gemini-2.0-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["MCP-JOOMLA"]
}
```
