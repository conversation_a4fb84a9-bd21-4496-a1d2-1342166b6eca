{"name": "@ibraheem4/eventbrite-mcp", "version": "1.0.1", "description": "An Eventbrite MCP server for interacting with Eventbrite's API", "type": "module", "bin": {"@ibraheem4/eventbrite-mcp": "./build/index.js", "eventbrite-mcp": "./build/index.js"}, "files": ["build"], "scripts": {"build": "tsc && chmod +x build/index.js", "prepare": "npm run build", "watch": "tsc --watch", "inspector": "node ./scripts/direct-inspector.js", "test": "node --experimental-vm-modules node_modules/jest/bin/jest.js src", "publish:patch": "npm version patch && npm publish", "publish:minor": "npm version minor && npm publish", "publish:major": "npm version major && npm publish"}, "dependencies": {"@modelcontextprotocol/sdk": "0.6.0", "axios": "1.8.1", "dotenv": "16.4.7", "http-proxy": "^1.18.1"}, "devDependencies": {"@jest/globals": "29.7.0", "@types/jest": "29.5.14", "@types/node": "^20.11.24", "jest": "29.7.0", "ts-jest": "29.2.6", "typescript": "^5.3.3"}, "keywords": ["eventbrite", "mcp", "model-context-protocol", "cli", "roo", "cline", "cursor", "windsurf", "claude"], "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/ibraheem4/eventbrite-mcp"}, "publishConfig": {"access": "public"}, "engines": {"node": ">=18"}}