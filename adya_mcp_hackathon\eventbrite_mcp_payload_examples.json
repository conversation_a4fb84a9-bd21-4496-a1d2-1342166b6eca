{"eventbrite_mcp_working_payload": {"selected_server_credentials": {"EVENTBRITE_MCP": {"api_key": "********************"}}, "client_details": {"api_key": "AIzaSyA3drnNDGAhFE-8MsJ9CsAkWm6QLNuwad8", "temperature": 0.1, "max_token": 20000, "input": "Use the get_categories tool to retrieve all available event categories from Eventbrite", "input_type": "text", "prompt": "you are a helpful assistant", "chat_model": "gemini-2.5-flash", "chat_history": [{"role": "user", "content": "Hello"}], "base_url": "https://www.eventbriteapi.com/v3/", "bearer_token": "********************"}, "selected_client": "MCP_CLIENT_GEMINI", "selected_servers": ["EVENTBRITE_MCP"]}, "eventbrite_working_tools": {"fully_working": [{"tool_name": "get_categories", "description": "Retrieve all available event categories", "status": "✅ WORKING", "example_input": "Use the get_categories tool to retrieve all available event categories from Eventbrite"}, {"tool_name": "search_events", "description": "Search for events (uses organization events)", "status": "✅ WORKING", "example_input": "Use the search_events tool to search for events with query \"test\""}, {"tool_name": "get_organization_events", "description": "Get events for a specific organization", "status": "✅ WORKING", "example_input": "Use the get_organization_events tool to get events for organization ID \"2829748307341\""}], "limited_permissions": [{"tool_name": "get_event", "description": "Get details for a specific event", "status": "❌ OAuth token invalid", "note": "Token has limited permissions for event-specific endpoints"}, {"tool_name": "get_user_profile", "description": "Get current user profile information", "status": "❌ OAuth token invalid", "note": "Token has limited permissions for user-specific endpoints"}, {"tool_name": "get_event_ticket_classes", "description": "Get ticket classes for an event", "status": "❌ OAuth token invalid", "note": "Token has limited permissions for event-specific endpoints"}, {"tool_name": "get_event_orders", "description": "Get orders for an event", "status": "❌ OAuth token invalid", "note": "Token has limited permissions for event-specific endpoints"}, {"tool_name": "get_event_discounts", "description": "Get discounts for an event", "status": "❌ OAuth token invalid", "note": "Token has limited permissions for event-specific endpoints"}], "api_endpoint_issues": [{"tool_name": "get_event_attendees_summary", "description": "Get attendee summary for an event", "status": "❌ 404 Not Found", "note": "API endpoint may not exist or require different parameters"}, {"tool_name": "get_venue", "description": "Get venue information", "status": "⚠️ Requires venue_id", "note": "Needs a valid venue_id to test"}]}, "test_data": {"organization_id": "2829748307341", "working_event_id": "1477820674419", "working_event_id_2": "1477837986199", "user_email": "<PERSON><PERSON><PERSON><PERSON><EMAIL>", "user_name": "<PERSON><PERSON><PERSON>"}, "configuration_updates": {"server_config_file": "adya_mcp_hackathon/mcp_servers/python/clients/src/client_and_server_config.py", "updated_env_var": "EVENTBRITE_API_KEY: ********************", "server_source_file": "adya_mcp_hackathon/mcp_servers/js/servers/eventbrite-mcp-main/src/index.ts", "updated_fallback_token": "const API_KEY = process.env.EVENTBRITE_API_KEY || \"********************\";"}}