import requests

token = "NUYEGTUZJIAFB4DA32DB"
headers = {"Authorization": f"Bearer {token}"}

# Get organization ID
org_response = requests.get("https://www.eventbriteapi.com/v3/users/me/organizations/", headers=headers)
org_id = org_response.json()["organizations"][0]["id"]
print(f"Organization ID: {org_id}")

# Use the valid event ID
event_id = "1477820674419"  # From get_organization_events response
venue_id = None  # No venue assigned yet; will address below

# Test all endpoints
endpoints = [
    ("get_categories", "https://www.eventbriteapi.com/v3/categories/"),
    ("search_events", "https://www.eventbriteapi.com/v3/events/search/?q=test"),
    ("get_event", f"https://www.eventbriteapi.com/v3/events/{event_id}/"),
    ("get_venue", f"https://www.eventbriteapi.com/v3/venues/{venue_id}/" if venue_id else None),
    ("get_event_attendees_summary", f"https://www.eventbriteapi.com/v3/events/{event_id}/attendees/summary/"),
    ("get_event_ticket_classes", f"https://www.eventbriteapi.com/v3/events/{event_id}/ticket_classes/"),
    ("get_event_orders", f"https://www.eventbriteapi.com/v3/events/{event_id}/orders/"),
    ("get_organization_events", f"https://www.eventbriteapi.com/v3/organizations/{org_id}/events/"),
    ("get_event_discounts", f"https://www.eventbriteapi.com/v3/events/{event_id}/discounts/"),
    ("get_user_profile", "https://www.eventbriteapi.com/v3/users/me/")
]

for name, url in endpoints:
    try:
        if url is None:  # Skip get_venue if no venue_id
            print(f"\nTesting {name}: Skipped (no venue_id available)")
            continue
        response = requests.get(url, headers=headers)
        print(f"\nTesting {name}:")
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
    except Exception as e:
        print(f"\nTesting {name} failed: {str(e)}")