import requests

token = "NUYEGTUZJIAFB4DA32DB"  # Replace with new token if invalid
headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}

# Get organization ID
org_response = requests.get("https://www.eventbriteapi.com/v3/users/me/organizations/", headers=headers)
if org_response.status_code != 200:
    print(f"Failed to get org_id: {org_response.status_code}, {org_response.json()}")
    exit()
org_id = org_response.json()["organizations"][0]["id"]
print(f"Organization ID: {org_id}")

# Use live event ID
event_id = "1477837986199"

# Create a venue
venue_data = {
    "venue": {
        "name": "Test Venue",
        "address": {
            "address_1": "123 Main St",
            "city": "San Francisco",
            "region": "CA",
            "postal_code": "94105",
            "country": "US"
        }
    }
}
venue_response = requests.post(f"https://www.eventbriteapi.com/v3/organizations/{org_id}/venues/", headers=headers, json=venue_data)
venue_id = venue_response.json().get("id") if venue_response.status_code == 200 else None
print(f"Created venue with ID: {venue_id}")

# Assign venue to event
if venue_id:
    event_update = {"event": {"venue_id": venue_id}}
    response = requests.post(f"https://www.eventbriteapi.com/v3/events/{event_id}/", headers=headers, json=event_update)
    print(f"Assigned venue {venue_id} to event {event_id}: {response.status_code}")

# Create a ticket class
ticket_data = {
    "ticket_class": {
        "name": "General Admission",
        "quantity_total": 100,
        "cost": "USD,0",
        "sales_start": "2025-07-01T00:00:00Z",
        "sales_end": "2025-08-18T00:00:00Z"
    }
}
ticket_response = requests.post(f"https://www.eventbriteapi.com/v3/events/{event_id}/ticket_classes/", headers=headers, json=ticket_data)
print(f"Created ticket class: {ticket_response.status_code}")

# Create a discount
discount_data = {
    "discount": {
        "type": "access",
        "code": "TEST10",
        "percent_off": "10",
        "event_id": event_id
    }
}
discount_response = requests.post(f"https://www.eventbriteapi.com/v3/events/{event_id}/discounts/", headers=headers, json=discount_data)
print(f"Created discount: {discount_response.status_code}")

# Test all endpoints
endpoints = [
    ("get_categories", "https://www.eventbriteapi.com/v3/categories/"),
    ("search_events", "https://www.eventbriteapi.com/v3/events/search/?q=adya"),
    ("get_event", f"https://www.eventbriteapi.com/v3/events/{event_id}/"),
    ("get_venue", f"https://www.eventbriteapi.com/v3/venues/{venue_id}/" if venue_id else None),
    ("get_event_attendees_summary", f"https://www.eventbriteapi.com/v3/events/{event_id}/attendees/summary/"),
    ("get_event_ticket_classes", f"https://www.eventbriteapi.com/v3/events/{event_id}/ticket_classes/"),
    ("get_event_orders", f"https://www.eventbriteapi.com/v3/events/{event_id}/orders/"),
    ("get_organization_events", f"https://www.eventbriteapi.com/v3/organizations/{org_id}/events/"),
    ("get_event_discounts", f"https://www.eventbriteapi.com/v3/events/{event_id}/discounts/"),
    ("get_user_profile", "https://www.eventbriteapi.com/v3/users/me/")
]

for name, url in endpoints:
    try:
        if url is None:
            print(f"\nTesting {name}: Skipped (no venue_id available)")
            continue
        response = requests.get(url, headers=headers)
        print(f"\nTesting {name}:")
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
    except Exception as e:
        print(f"\nTesting {name} failed: {str(e)}")