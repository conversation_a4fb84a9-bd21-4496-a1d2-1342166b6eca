# Eventbrite MCP Server Complete Payloads - Gemini 2.5 Flash

## Overview
Complete payload documentation for all 10 Eventbrite MCP tools using Gemini 2.5 Flash API.

**API Configuration:**
- Model: `gemini-2.5-flash`
- API Key: `AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU`
- Client: `MCP_CLIENT_GEMINI`

**Server Status:** ✅ CONFIGURED AND READY - 10 Eventbrite API tools available

**Note:** Eventbrite MCP requires an Eventbrite API key for authentication.

**Credentials Required:**
- API Key: Your Eventbrite API key

---

## Event Search and Discovery Tools

### 1. search_events

```json
{
    "selected_server_credentials": {
        "EVENTBRITE_MCP": {
            "api_key": "********************"
        }
    },
    "client_details": {
        "api_key": "AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Use the search_events tool to search for events with query 'tech conference' in location latitude=37.7749, longitude=-122.4194, within='10km'",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "chat_model": "gemini-2.5-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["EVENTBRITE_MCP"]
}
```

### 2. get_event

```json
{
    "selected_server_credentials": {
        "EVENTBRITE_MCP": {
            "api_key": "YOUR_EVENTBRITE_API_KEY"
        }
    },
    "client_details": {
        "api_key": "AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Use the get_event tool to get detailed information about event with event_id '123456789'",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "chat_model": "gemini-2.5-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["EVENTBRITE_MCP"]
}
```

### 3. get_categories

```json
{
    "selected_server_credentials": {
        "EVENTBRITE_MCP": {
            "api_key": "YOUR_EVENTBRITE_API_KEY"
        }
    },
    "client_details": {
        "api_key": "AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Use the get_categories tool to get a list of all Eventbrite event categories",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "chat_model": "gemini-2.5-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["EVENTBRITE_MCP"]
}
```

---

## Venue and Location Tools

### 4. get_venue

```json
{
    "selected_server_credentials": {
        "EVENTBRITE_MCP": {
            "api_key": "YOUR_EVENTBRITE_API_KEY"
        }
    },
    "client_details": {
        "api_key": "AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Use the get_venue tool to get information about venue with venue_id '987654321'",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "chat_model": "gemini-2.5-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["EVENTBRITE_MCP"]
}
```

---

## Event Management Tools

### 5. get_event_attendees_summary

```json
{
    "selected_server_credentials": {
        "EVENTBRITE_MCP": {
            "api_key": "YOUR_EVENTBRITE_API_KEY"
        }
    },
    "client_details": {
        "api_key": "AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Use the get_event_attendees_summary tool to get attendee count and summary for event with event_id '123456789'",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "chat_model": "gemini-2.5-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["EVENTBRITE_MCP"]
}
```

### 6. get_event_ticket_classes

```json
{
    "selected_server_credentials": {
        "EVENTBRITE_MCP": {
            "api_key": "YOUR_EVENTBRITE_API_KEY"
        }
    },
    "client_details": {
        "api_key": "AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Use the get_event_ticket_classes tool to get ticket classes and types for event with event_id '123456789'",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "chat_model": "gemini-2.5-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["EVENTBRITE_MCP"]
}
```

### 7. get_event_orders

```json
{
    "selected_server_credentials": {
        "EVENTBRITE_MCP": {
            "api_key": "YOUR_EVENTBRITE_API_KEY"
        }
    },
    "client_details": {
        "api_key": "AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Use the get_event_orders tool to get orders for event with event_id '123456789' and status 'placed'",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "chat_model": "gemini-2.5-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["EVENTBRITE_MCP"]
}
```

### 8. get_organization_events

```json
{
    "selected_server_credentials": {
        "EVENTBRITE_MCP": {
            "api_key": "YOUR_EVENTBRITE_API_KEY"
        }
    },
    "client_details": {
        "api_key": "AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Use the get_organization_events tool to get events for organization with status 'live' and time_filter 'current_future'",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "chat_model": "gemini-2.5-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["EVENTBRITE_MCP"]
}
```

### 9. get_event_discounts

```json
{
    "selected_server_credentials": {
        "EVENTBRITE_MCP": {
            "api_key": "YOUR_EVENTBRITE_API_KEY"
        }
    },
    "client_details": {
        "api_key": "AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Use the get_event_discounts tool to get discount codes for event with event_id '123456789'",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "chat_model": "gemini-2.5-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["EVENTBRITE_MCP"]
}
```

### 10. get_user_profile

```json
{
    "selected_server_credentials": {
        "EVENTBRITE_MCP": {
            "api_key": "YOUR_EVENTBRITE_API_KEY"
        }
    },
    "client_details": {
        "api_key": "AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU",
        "temperature": 0.1,
        "max_token": 20000,
        "input": "Use the get_user_profile tool to get your Eventbrite user profile and account information",
        "input_type": "text",
        "prompt": "you are a helpful assistant",
        "chat_model": "gemini-2.5-flash",
        "chat_history": [{"role": "user", "content": "Hello"}]
    },
    "selected_client": "MCP_CLIENT_GEMINI",
    "selected_servers": ["EVENTBRITE_MCP"]
}
```

---

## Summary

**Total Eventbrite MCP Tools: 10 Tools**

**Tool Categories:**
- **Event Search and Discovery** (3): search_events, get_event, get_categories
- **Venue and Location** (1): get_venue
- **Event Management** (6): get_event_attendees_summary, get_event_ticket_classes, get_event_orders, get_organization_events, get_event_discounts, get_user_profile

**All payloads use:**
- Model: `gemini-2.5-flash`
- API Key: `AIzaSyASYMzBmcbmqnUDq1edQGI5xC9NqxbLWoU`
- Client: `MCP_CLIENT_GEMINI`

**Status:** ✅ ALL 10 EVENTBRITE API TOOLS DOCUMENTED AND READY FOR USE

**Note:** Replace `YOUR_EVENTBRITE_API_KEY` with your actual Eventbrite API key to use these tools.
